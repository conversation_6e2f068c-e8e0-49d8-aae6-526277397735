/**
 * 新建弹窗：Catalog、Schema
 * * 注意：
 * * * 1、不包括 Volume、Table、Operator的弹窗，该弹窗为简单的名称 + 描述
 * <AUTHOR>
 */

import React, {useContext, useState} from 'react';
import {Form, Input, Modal, toast} from 'acud';
import {RuleObject} from 'acud/lib/form';
import {createCatalog, createSchema, createTable, createOperator} from '@api/metaRequest';
import './index.less';
import {formItemLayout, MetaCnNameMap} from '../constants';
import {WorkspaceContext} from '@pages/index';

type IrequestFun = typeof createCatalog | typeof createSchema | typeof createTable | typeof createOperator;

interface IModalCreateNameProps {
  visible: boolean; // 弹窗可见
  onCancel: () => void;
  title: string; // 弹窗标题 & 表单名称的 label
  nameRules?: RuleObject[]; // 表单名称校验规则
  requestFun: IrequestFun; // 点击确定创建的请求方法
  requestParams?: {[key: string]: any}; // 额外的请求参数，例如：Catalog 创建需要 workspaceId
  successCallback: (...args: any[]) => any; // requestFun 请求成功后回调
  limitLength?: number; // 输入框最大长度
  commentLimitLength?: number; // 描述输入框最大长度
  forbidIfLimit?: boolean; // 超出长度是否禁用
  showDescription?: boolean; // 是否显示描述输入框，默认为 true
}

/**
 * 创建 catalog/schema/table 弹窗
 */
const ModalCreateName = (props: IModalCreateNameProps) => {
  const {
    visible,
    onCancel,
    title,
    requestFun,
    requestParams,
    nameRules = [],
    successCallback,
    limitLength = 64,
    commentLimitLength = 150,
    forbidIfLimit = true,
    showDescription = true
  } = props;

  const [form] = Form.useForm();

  const {workspaceId} = useContext(WorkspaceContext);

  // 确定按钮 loading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const handleCancel = () => {
    setConfirmLoading(false);
    onCancel();
  };

  const handleOk = async () => {
    setConfirmLoading(true);
    try {
      console.log('1 :>> ', 1);
      console.log('开始验证表单字段，nameRules:', nameRules);

      // 获取表单字段值
      const formValues = form.getFieldsValue();
      console.log('表单当前值:', formValues);

      // 手动触发特定字段验证
      await form.validateFields(['name']);
      console.log('2 :>> ', nameRules);
    } catch (error) {
      console.log('3 :>> ', 3);
      console.log('表单验证失败:', error);
      setConfirmLoading(false);
      return;
    }
    // formData 值为： {name:xxx, comment:xxx}
    const formData = form.getFieldsValue();
    try {
      const res = await requestFun(workspaceId, {...requestParams, ...formData});
      if (res?.success != true) {
        return;
      }
      toast.success({
        message: '创建成功！',
        duration: 5
      });
      // 请求成功后的回调
      successCallback(formData);
      handleCancel();
    } catch {
      return;
    } finally {
      setConfirmLoading(false);
    }
  };

  return (
    <Modal
      className="modal-form-reset-acud"
      title={`创建${MetaCnNameMap[title]}`}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      afterClose={() => form.resetFields()}
      confirmLoading={confirmLoading}
    >
      <Form {...formItemLayout} form={form} name="create-name-modal">
        <Form.Item
          name="name"
          label={`${MetaCnNameMap[title]}名称`}
          rules={[
            {
              required: true,
              message: '名称不可为空'
            },
            ...nameRules
          ]}
          validateTrigger={['onBlur', 'onChange']}
        >
          <Input placeholder="请输入名称" limitLength={limitLength} forbidIfLimit={forbidIfLimit} />
        </Form.Item>
        {showDescription ? (
          <Form.Item name="comment" label="描述">
            <Input.TextArea
              placeholder="请输入描述"
              allowClear={false}
              limitLength={commentLimitLength}
              forbidIfLimit={forbidIfLimit}
            />
          </Form.Item>
        ) : null}
      </Form>
    </Modal>
  );
};

export default ModalCreateName;
